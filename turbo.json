{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**", "build/**"]}, "dev": {"cache": false, "persistent": true}, "lint": {"outputs": []}, "lint:fix": {"outputs": []}, "type-check": {"dependsOn": ["^build"], "outputs": []}, "test": {"dependsOn": ["build"], "outputs": ["coverage/**"]}, "test:unit": {"dependsOn": ["build"], "outputs": ["coverage/**"]}, "test:integration": {"dependsOn": ["build"], "outputs": ["coverage/**"]}, "test:e2e": {"dependsOn": ["build"], "outputs": ["coverage/**"]}, "clean": {"cache": false}, "db:migrate": {"cache": false}, "db:seed": {"cache": false}, "db:generate": {"dependsOn": ["^db:generate"], "outputs": ["node_modules/.prisma/**"]}}}