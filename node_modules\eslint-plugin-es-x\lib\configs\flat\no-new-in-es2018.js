/**
 * DON'T EDIT THIS FILE.
 * This file was generated by "scripts/update-lib-flat-configs.js" script.
 */
"use strict"

module.exports = {
    plugins: {
        get "es-x"() {
            return require("../../index.js")
        },
    },
    rules: {
        "es-x/no-async-iteration": "error",
        "es-x/no-malformed-template-literals": "error",
        "es-x/no-promise-prototype-finally": "error",
        "es-x/no-regexp-lookbehind-assertions": "error",
        "es-x/no-regexp-named-capture-groups": "error",
        "es-x/no-regexp-s-flag": "error",
        "es-x/no-regexp-unicode-property-escapes": "error",
        "es-x/no-rest-spread-properties": "error",
    },
}
