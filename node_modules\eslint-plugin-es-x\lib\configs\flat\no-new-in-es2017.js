/**
 * DON'T EDIT THIS FILE.
 * This file was generated by "scripts/update-lib-flat-configs.js" script.
 */
"use strict"

module.exports = {
    plugins: {
        get "es-x"() {
            return require("../../index.js")
        },
    },
    rules: {
        "es-x/no-async-functions": "error",
        "es-x/no-atomics": "error",
        "es-x/no-object-entries": "error",
        "es-x/no-object-getownpropertydescriptors": "error",
        "es-x/no-object-values": "error",
        "es-x/no-shared-array-buffer": "error",
        "es-x/no-string-prototype-padstart-padend": "error",
        "es-x/no-trailing-function-commas": "error",
    },
}
