"use strict"

const pkg = require("../package.json")
const esmConfig = require("./configs/recommended-module")
const cjsConfig = require("./configs/recommended-script")
const recommendedConfig = require("./configs/recommended")

const rules = {
    "callback-return": require("./rules/callback-return"),
    "exports-style": require("./rules/exports-style"),
    "file-extension-in-import": require("./rules/file-extension-in-import"),
    "global-require": require("./rules/global-require"),
    "handle-callback-err": require("./rules/handle-callback-err"),
    "no-callback-literal": require("./rules/no-callback-literal"),
    "no-deprecated-api": require("./rules/no-deprecated-api"),
    "no-exports-assign": require("./rules/no-exports-assign"),
    "no-extraneous-import": require("./rules/no-extraneous-import"),
    "no-extraneous-require": require("./rules/no-extraneous-require"),
    "no-missing-import": require("./rules/no-missing-import"),
    "no-missing-require": require("./rules/no-missing-require"),
    "no-mixed-requires": require("./rules/no-mixed-requires"),
    "no-new-require": require("./rules/no-new-require"),
    "no-path-concat": require("./rules/no-path-concat"),
    "no-process-env": require("./rules/no-process-env"),
    "no-process-exit": require("./rules/no-process-exit"),
    "no-restricted-import": require("./rules/no-restricted-import"),
    "no-restricted-require": require("./rules/no-restricted-require"),
    "no-sync": require("./rules/no-sync"),
    "no-unpublished-bin": require("./rules/no-unpublished-bin"),
    "no-unpublished-import": require("./rules/no-unpublished-import"),
    "no-unpublished-require": require("./rules/no-unpublished-require"),
    "no-unsupported-features/es-builtins": require("./rules/no-unsupported-features/es-builtins"),
    "no-unsupported-features/es-syntax": require("./rules/no-unsupported-features/es-syntax"),
    "no-unsupported-features/node-builtins": require("./rules/no-unsupported-features/node-builtins"),
    "prefer-global/buffer": require("./rules/prefer-global/buffer"),
    "prefer-global/console": require("./rules/prefer-global/console"),
    "prefer-global/process": require("./rules/prefer-global/process"),
    "prefer-global/text-decoder": require("./rules/prefer-global/text-decoder"),
    "prefer-global/text-encoder": require("./rules/prefer-global/text-encoder"),
    "prefer-global/url-search-params": require("./rules/prefer-global/url-search-params"),
    "prefer-global/url": require("./rules/prefer-global/url"),
    "prefer-promises/dns": require("./rules/prefer-promises/dns"),
    "prefer-promises/fs": require("./rules/prefer-promises/fs"),
    "process-exit-as-throw": require("./rules/process-exit-as-throw"),
    shebang: require("./rules/shebang"),

    // Deprecated rules.
    "no-hide-core-modules": require("./rules/no-hide-core-modules"),
    "no-unsupported-features": require("./rules/no-unsupported-features"),
}

const mod = {
    meta: {
        name: pkg.name,
        version: pkg.version,
    },
    rules,
}

// set configs, e.g. mod.configs["recommended-module"]
// do not defined in the mod obj - to avoid circular dependency
mod.configs = {
    "recommended-module": { plugins: ["n"], ...esmConfig.eslintrc },
    "recommended-script": { plugins: ["n"], ...cjsConfig.eslintrc },
    recommended: { plugins: ["n"], ...recommendedConfig.eslintrc },
    "flat/recommended-module": { plugins: { n: mod }, ...esmConfig.flat },
    "flat/recommended-script": { plugins: { n: mod }, ...cjsConfig.flat },
    "flat/recommended": { plugins: { n: mod }, ...recommendedConfig.flat },
    "flat/mixed-esm-and-cjs": [
        { plugins: { n: mod }, files: ["**/*.js"], ...recommendedConfig.flat },
        { plugins: { n: mod }, files: ["**/*.mjs"], ...esmConfig.flat },
        { plugins: { n: mod }, files: ["**/*.cjs"], ...cjsConfig.flat },
    ],
}

module.exports = mod
