import NextAuth, { NextAuthOptions } from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
// import { PrismaAdapter } from '@next-auth/prisma-adapter';
// import { prisma } from '@freela/database';
// import { Prisma } from '@prisma/client';

export const authOptions: NextAuthOptions = {
  // adapter: PrismaAdapter(prisma), // Temporarily disabled for testing
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID || '',
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code"
        }
      }
    }),
  ],
  pages: {
    signIn: '/',
    error: '/auth/error',
  },
  callbacks: {
    async signIn({ user, account, profile }) {
      try {
        console.log('🔐 SignIn callback triggered:', {
          user: user.email,
          provider: account?.provider,
          profile: profile?.name
        });

        if (account?.provider === 'google') {
          console.log('✅ Google OAuth sign-in successful for:', user.email);
          // For now, we'll skip database operations and just allow sign-in
          // TODO: Re-enable database operations once connection is fixed
          return true;
        }
        return true;
      } catch (error) {
        console.error('❌ Error during sign in:', error);
        return false;
      }
    },
    async jwt({ token, user, account, profile }) {
      console.log('🔑 JWT callback triggered:', {
        hasUser: !!user,
        tokenEmail: token.email,
        userEmail: user?.email
      });

      if (user) {
        console.log('👤 Setting up JWT token for new user:', user.email);
        // For testing, we'll set default values without database lookup
        token.id = user.id || 'temp-id';
        token.role = 'CLIENT'; // Default role
        token.status = 'ACTIVE';
        token.language = 'ar';
        token.firstName = user.name?.split(' ')[0] || 'User';
        token.lastName = user.name?.split(' ').slice(1).join(' ') || '';
        token.avatar = user.image || null;
        token.hasCompletedOnboarding = false; // Always false for new users

        console.log('✅ JWT token configured:', {
          id: token.id,
          role: token.role,
          hasCompletedOnboarding: token.hasCompletedOnboarding
        });
      }
      return token;
    },
    async session({ session, token }) {
      console.log('📋 Session callback triggered:', {
        tokenEmail: token.email,
        sessionEmail: session.user?.email,
        hasCompletedOnboarding: token.hasCompletedOnboarding
      });

      if (token) {
        session.user.id = token.id as string;
        session.user.role = token.role as string;
        session.user.status = token.status as string;
        session.user.language = token.language as string;
        session.user.firstName = token.firstName as string;
        session.user.lastName = token.lastName as string;
        session.user.avatar = token.avatar as string;
        session.user.hasCompletedOnboarding = token.hasCompletedOnboarding as boolean;

        console.log('✅ Session configured:', {
          id: session.user.id,
          role: session.user.role,
          hasCompletedOnboarding: session.user.hasCompletedOnboarding
        });
      }
      return session;
    },
    async redirect({ url, baseUrl }) {
      // Handle role-based redirects after successful authentication
      try {
        console.log('🔄 NextAuth redirect called with:', { url, baseUrl, token });

        // For now, redirect all users to AI onboarding
        // TODO: Check if user has completed onboarding from database

        // Redirect ALL new users to AI onboarding (MANDATORY)
        console.log('🤖 Redirecting to AI onboarding for new user');
        return `${baseUrl}/ai-onboarding`;

      } catch (error) {
        console.error('❌ Redirect error:', error);
        return `${baseUrl}/?auth=error`;
      }
    }
  },
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  events: {
    async signIn({ user, account }) {
      console.log(`🎉 User ${user.email} signed in with ${account?.provider}`);
      // TODO: Re-enable database operations once connection is fixed
    },
    async signOut({ token }) {
      console.log(`👋 User ${token?.email} signed out`);
    }
  },
  debug: process.env.NODE_ENV === 'development',
};

export default NextAuth(authOptions);
