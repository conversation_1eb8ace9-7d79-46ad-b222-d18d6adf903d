/**
 * DON'T EDIT THIS FILE.
 * This file was generated by "scripts/update-lib-configs.js" script.
 */
"use strict"

module.exports = {
    plugins: ["es-x"],
    rules: {
        "es-x/no-arbitrary-module-namespace-names": "error",
        "es-x/no-array-string-prototype-at": "error",
        "es-x/no-class-fields": "error",
        "es-x/no-class-static-block": "error",
        "es-x/no-error-cause": "error",
        "es-x/no-object-hasown": "error",
        "es-x/no-private-in": "error",
        "es-x/no-regexp-d-flag": "error",
        "es-x/no-regexp-unicode-property-escapes-2022": "error",
        "es-x/no-top-level-await": "error",
    },
}
