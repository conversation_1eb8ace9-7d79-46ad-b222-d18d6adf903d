/**
 * DON'T EDIT THIS FILE.
 * This file was generated automatically by 'scripts/update-lib-index.js'.
 */
"use strict"

const { printWarningOfDeprecatedConfig } = require("./utils")
const { version, name } = require("../package.json")

module.exports = {
    meta: { version, name },
    configs: {
        "flat/no-new-in-es5": require("./configs/flat/no-new-in-es5"),
        "flat/no-new-in-es2015": require("./configs/flat/no-new-in-es2015"),
        "flat/no-new-in-es2015-intl-api": require("./configs/flat/no-new-in-es2015-intl-api"),
        "flat/no-new-in-es2016": require("./configs/flat/no-new-in-es2016"),
        "flat/no-new-in-es2016-intl-api": require("./configs/flat/no-new-in-es2016-intl-api"),
        "flat/no-new-in-es2017": require("./configs/flat/no-new-in-es2017"),
        "flat/no-new-in-es2017-intl-api": require("./configs/flat/no-new-in-es2017-intl-api"),
        "flat/no-new-in-es2018": require("./configs/flat/no-new-in-es2018"),
        "flat/no-new-in-es2018-intl-api": require("./configs/flat/no-new-in-es2018-intl-api"),
        "flat/no-new-in-es2019": require("./configs/flat/no-new-in-es2019"),
        "flat/no-new-in-es2019-intl-api": require("./configs/flat/no-new-in-es2019-intl-api"),
        "flat/no-new-in-es2020": require("./configs/flat/no-new-in-es2020"),
        "flat/no-new-in-es2020-intl-api": require("./configs/flat/no-new-in-es2020-intl-api"),
        "flat/no-new-in-es2021": require("./configs/flat/no-new-in-es2021"),
        "flat/no-new-in-es2021-intl-api": require("./configs/flat/no-new-in-es2021-intl-api"),
        "flat/no-new-in-es2022": require("./configs/flat/no-new-in-es2022"),
        "flat/no-new-in-es2022-intl-api": require("./configs/flat/no-new-in-es2022-intl-api"),
        "flat/no-new-in-es2023": require("./configs/flat/no-new-in-es2023"),
        "flat/no-new-in-es2023-intl-api": require("./configs/flat/no-new-in-es2023-intl-api"),
        "flat/no-new-in-esnext": require("./configs/flat/no-new-in-esnext"),
        "flat/no-new-in-esnext-intl-api": require("./configs/flat/no-new-in-esnext-intl-api"),
        "flat/no-set-methods": require("./configs/flat/no-set-methods"),
        "flat/restrict-to-es-intl-api-1st-edition": require("./configs/flat/restrict-to-es-intl-api-1st-edition"),
        "flat/restrict-to-es3": require("./configs/flat/restrict-to-es3"),
        "flat/restrict-to-es5": require("./configs/flat/restrict-to-es5"),
        "flat/restrict-to-es2015": require("./configs/flat/restrict-to-es2015"),
        "flat/restrict-to-es2015-intl-api": require("./configs/flat/restrict-to-es2015-intl-api"),
        "flat/restrict-to-es2016": require("./configs/flat/restrict-to-es2016"),
        "flat/restrict-to-es2016-intl-api": require("./configs/flat/restrict-to-es2016-intl-api"),
        "flat/restrict-to-es2017": require("./configs/flat/restrict-to-es2017"),
        "flat/restrict-to-es2017-intl-api": require("./configs/flat/restrict-to-es2017-intl-api"),
        "flat/restrict-to-es2018": require("./configs/flat/restrict-to-es2018"),
        "flat/restrict-to-es2018-intl-api": require("./configs/flat/restrict-to-es2018-intl-api"),
        "flat/restrict-to-es2019": require("./configs/flat/restrict-to-es2019"),
        "flat/restrict-to-es2019-intl-api": require("./configs/flat/restrict-to-es2019-intl-api"),
        "flat/restrict-to-es2020": require("./configs/flat/restrict-to-es2020"),
        "flat/restrict-to-es2020-intl-api": require("./configs/flat/restrict-to-es2020-intl-api"),
        "flat/restrict-to-es2021": require("./configs/flat/restrict-to-es2021"),
        "flat/restrict-to-es2021-intl-api": require("./configs/flat/restrict-to-es2021-intl-api"),
        "flat/restrict-to-es2022": require("./configs/flat/restrict-to-es2022"),
        "flat/restrict-to-es2022-intl-api": require("./configs/flat/restrict-to-es2022-intl-api"),
        "no-new-in-es5": require("./configs/no-new-in-es5"),
        "no-new-in-es2015": require("./configs/no-new-in-es2015"),
        "no-new-in-es2015-intl-api": require("./configs/no-new-in-es2015-intl-api"),
        "no-new-in-es2016": require("./configs/no-new-in-es2016"),
        "no-new-in-es2016-intl-api": require("./configs/no-new-in-es2016-intl-api"),
        "no-new-in-es2017": require("./configs/no-new-in-es2017"),
        "no-new-in-es2017-intl-api": require("./configs/no-new-in-es2017-intl-api"),
        "no-new-in-es2018": require("./configs/no-new-in-es2018"),
        "no-new-in-es2018-intl-api": require("./configs/no-new-in-es2018-intl-api"),
        "no-new-in-es2019": require("./configs/no-new-in-es2019"),
        "no-new-in-es2019-intl-api": require("./configs/no-new-in-es2019-intl-api"),
        "no-new-in-es2020": require("./configs/no-new-in-es2020"),
        "no-new-in-es2020-intl-api": require("./configs/no-new-in-es2020-intl-api"),
        "no-new-in-es2021": require("./configs/no-new-in-es2021"),
        "no-new-in-es2021-intl-api": require("./configs/no-new-in-es2021-intl-api"),
        "no-new-in-es2022": require("./configs/no-new-in-es2022"),
        "no-new-in-es2022-intl-api": require("./configs/no-new-in-es2022-intl-api"),
        "no-new-in-es2023": require("./configs/no-new-in-es2023"),
        "no-new-in-es2023-intl-api": require("./configs/no-new-in-es2023-intl-api"),
        "no-new-in-esnext": require("./configs/no-new-in-esnext"),
        "no-new-in-esnext-intl-api": require("./configs/no-new-in-esnext-intl-api"),
        "no-set-methods": require("./configs/no-set-methods"),
        "restrict-to-es-intl-api-1st-edition": require("./configs/restrict-to-es-intl-api-1st-edition"),
        "restrict-to-es3": require("./configs/restrict-to-es3"),
        "restrict-to-es5": require("./configs/restrict-to-es5"),
        "restrict-to-es2015": require("./configs/restrict-to-es2015"),
        "restrict-to-es2015-intl-api": require("./configs/restrict-to-es2015-intl-api"),
        "restrict-to-es2016": require("./configs/restrict-to-es2016"),
        "restrict-to-es2016-intl-api": require("./configs/restrict-to-es2016-intl-api"),
        "restrict-to-es2017": require("./configs/restrict-to-es2017"),
        "restrict-to-es2017-intl-api": require("./configs/restrict-to-es2017-intl-api"),
        "restrict-to-es2018": require("./configs/restrict-to-es2018"),
        "restrict-to-es2018-intl-api": require("./configs/restrict-to-es2018-intl-api"),
        "restrict-to-es2019": require("./configs/restrict-to-es2019"),
        "restrict-to-es2019-intl-api": require("./configs/restrict-to-es2019-intl-api"),
        "restrict-to-es2020": require("./configs/restrict-to-es2020"),
        "restrict-to-es2020-intl-api": require("./configs/restrict-to-es2020-intl-api"),
        "restrict-to-es2021": require("./configs/restrict-to-es2021"),
        "restrict-to-es2021-intl-api": require("./configs/restrict-to-es2021-intl-api"),
        "restrict-to-es2022": require("./configs/restrict-to-es2022"),
        "restrict-to-es2022-intl-api": require("./configs/restrict-to-es2022-intl-api"),
        get "no-5"() {
            printWarningOfDeprecatedConfig("no-5")
            return this["no-new-in-es5"]
        },
        get "no-2015"() {
            printWarningOfDeprecatedConfig("no-2015")
            return this["no-new-in-es2015"]
        },
        get "no-2016"() {
            printWarningOfDeprecatedConfig("no-2016")
            return this["no-new-in-es2016"]
        },
        get "no-2017"() {
            printWarningOfDeprecatedConfig("no-2017")
            return this["no-new-in-es2017"]
        },
        get "no-2018"() {
            printWarningOfDeprecatedConfig("no-2018")
            return this["no-new-in-es2018"]
        },
        get "no-2019"() {
            printWarningOfDeprecatedConfig("no-2019")
            return this["no-new-in-es2019"]
        },
    },
    rules: {
        "no-accessor-properties": require("./rules/no-accessor-properties"),
        "no-arbitrary-module-namespace-names": require("./rules/no-arbitrary-module-namespace-names"),
        "no-array-from": require("./rules/no-array-from"),
        "no-array-isarray": require("./rules/no-array-isarray"),
        "no-array-of": require("./rules/no-array-of"),
        "no-array-prototype-copywithin": require("./rules/no-array-prototype-copywithin"),
        "no-array-prototype-entries": require("./rules/no-array-prototype-entries"),
        "no-array-prototype-every": require("./rules/no-array-prototype-every"),
        "no-array-prototype-fill": require("./rules/no-array-prototype-fill"),
        "no-array-prototype-filter": require("./rules/no-array-prototype-filter"),
        "no-array-prototype-find": require("./rules/no-array-prototype-find"),
        "no-array-prototype-findindex": require("./rules/no-array-prototype-findindex"),
        "no-array-prototype-findlast-findlastindex": require("./rules/no-array-prototype-findlast-findlastindex"),
        "no-array-prototype-flat": require("./rules/no-array-prototype-flat"),
        "no-array-prototype-foreach": require("./rules/no-array-prototype-foreach"),
        "no-array-prototype-includes": require("./rules/no-array-prototype-includes"),
        "no-array-prototype-indexof": require("./rules/no-array-prototype-indexof"),
        "no-array-prototype-keys": require("./rules/no-array-prototype-keys"),
        "no-array-prototype-lastindexof": require("./rules/no-array-prototype-lastindexof"),
        "no-array-prototype-map": require("./rules/no-array-prototype-map"),
        "no-array-prototype-reduce": require("./rules/no-array-prototype-reduce"),
        "no-array-prototype-reduceright": require("./rules/no-array-prototype-reduceright"),
        "no-array-prototype-some": require("./rules/no-array-prototype-some"),
        "no-array-prototype-toreversed": require("./rules/no-array-prototype-toreversed"),
        "no-array-prototype-tosorted": require("./rules/no-array-prototype-tosorted"),
        "no-array-prototype-tospliced": require("./rules/no-array-prototype-tospliced"),
        "no-array-prototype-values": require("./rules/no-array-prototype-values"),
        "no-array-prototype-with": require("./rules/no-array-prototype-with"),
        "no-array-string-prototype-at": require("./rules/no-array-string-prototype-at"),
        "no-arraybuffer-prototype-transfer": require("./rules/no-arraybuffer-prototype-transfer"),
        "no-arrow-functions": require("./rules/no-arrow-functions"),
        "no-async-functions": require("./rules/no-async-functions"),
        "no-async-iteration": require("./rules/no-async-iteration"),
        "no-atomics": require("./rules/no-atomics"),
        "no-atomics-waitasync": require("./rules/no-atomics-waitasync"),
        "no-bigint": require("./rules/no-bigint"),
        "no-binary-numeric-literals": require("./rules/no-binary-numeric-literals"),
        "no-block-scoped-functions": require("./rules/no-block-scoped-functions"),
        "no-block-scoped-variables": require("./rules/no-block-scoped-variables"),
        "no-class-fields": require("./rules/no-class-fields"),
        "no-class-static-block": require("./rules/no-class-static-block"),
        "no-classes": require("./rules/no-classes"),
        "no-computed-properties": require("./rules/no-computed-properties"),
        "no-date-now": require("./rules/no-date-now"),
        "no-date-prototype-getyear-setyear": require("./rules/no-date-prototype-getyear-setyear"),
        "no-date-prototype-togmtstring": require("./rules/no-date-prototype-togmtstring"),
        "no-default-parameters": require("./rules/no-default-parameters"),
        "no-destructuring": require("./rules/no-destructuring"),
        "no-dynamic-import": require("./rules/no-dynamic-import"),
        "no-error-cause": require("./rules/no-error-cause"),
        "no-escape-unescape": require("./rules/no-escape-unescape"),
        "no-exponential-operators": require("./rules/no-exponential-operators"),
        "no-export-ns-from": require("./rules/no-export-ns-from"),
        "no-for-of-loops": require("./rules/no-for-of-loops"),
        "no-function-declarations-in-if-statement-clauses-without-block": require("./rules/no-function-declarations-in-if-statement-clauses-without-block"),
        "no-function-prototype-bind": require("./rules/no-function-prototype-bind"),
        "no-generators": require("./rules/no-generators"),
        "no-global-this": require("./rules/no-global-this"),
        "no-hashbang": require("./rules/no-hashbang"),
        "no-import-meta": require("./rules/no-import-meta"),
        "no-initializers-in-for-in": require("./rules/no-initializers-in-for-in"),
        "no-intl-datetimeformat-prototype-formatrange": require("./rules/no-intl-datetimeformat-prototype-formatrange"),
        "no-intl-datetimeformat-prototype-formattoparts": require("./rules/no-intl-datetimeformat-prototype-formattoparts"),
        "no-intl-displaynames": require("./rules/no-intl-displaynames"),
        "no-intl-getcanonicallocales": require("./rules/no-intl-getcanonicallocales"),
        "no-intl-listformat": require("./rules/no-intl-listformat"),
        "no-intl-locale": require("./rules/no-intl-locale"),
        "no-intl-numberformat-prototype-formatrange": require("./rules/no-intl-numberformat-prototype-formatrange"),
        "no-intl-numberformat-prototype-formatrangetoparts": require("./rules/no-intl-numberformat-prototype-formatrangetoparts"),
        "no-intl-numberformat-prototype-formattoparts": require("./rules/no-intl-numberformat-prototype-formattoparts"),
        "no-intl-pluralrules": require("./rules/no-intl-pluralrules"),
        "no-intl-pluralrules-prototype-selectrange": require("./rules/no-intl-pluralrules-prototype-selectrange"),
        "no-intl-relativetimeformat": require("./rules/no-intl-relativetimeformat"),
        "no-intl-segmenter": require("./rules/no-intl-segmenter"),
        "no-intl-supportedvaluesof": require("./rules/no-intl-supportedvaluesof"),
        "no-json": require("./rules/no-json"),
        "no-json-superset": require("./rules/no-json-superset"),
        "no-keyword-properties": require("./rules/no-keyword-properties"),
        "no-labelled-function-declarations": require("./rules/no-labelled-function-declarations"),
        "no-legacy-object-prototype-accessor-methods": require("./rules/no-legacy-object-prototype-accessor-methods"),
        "no-logical-assignment-operators": require("./rules/no-logical-assignment-operators"),
        "no-malformed-template-literals": require("./rules/no-malformed-template-literals"),
        "no-map": require("./rules/no-map"),
        "no-math-acosh": require("./rules/no-math-acosh"),
        "no-math-asinh": require("./rules/no-math-asinh"),
        "no-math-atanh": require("./rules/no-math-atanh"),
        "no-math-cbrt": require("./rules/no-math-cbrt"),
        "no-math-clz32": require("./rules/no-math-clz32"),
        "no-math-cosh": require("./rules/no-math-cosh"),
        "no-math-expm1": require("./rules/no-math-expm1"),
        "no-math-fround": require("./rules/no-math-fround"),
        "no-math-hypot": require("./rules/no-math-hypot"),
        "no-math-imul": require("./rules/no-math-imul"),
        "no-math-log1p": require("./rules/no-math-log1p"),
        "no-math-log2": require("./rules/no-math-log2"),
        "no-math-log10": require("./rules/no-math-log10"),
        "no-math-sign": require("./rules/no-math-sign"),
        "no-math-sinh": require("./rules/no-math-sinh"),
        "no-math-tanh": require("./rules/no-math-tanh"),
        "no-math-trunc": require("./rules/no-math-trunc"),
        "no-modules": require("./rules/no-modules"),
        "no-new-target": require("./rules/no-new-target"),
        "no-nullish-coalescing-operators": require("./rules/no-nullish-coalescing-operators"),
        "no-number-epsilon": require("./rules/no-number-epsilon"),
        "no-number-isfinite": require("./rules/no-number-isfinite"),
        "no-number-isinteger": require("./rules/no-number-isinteger"),
        "no-number-isnan": require("./rules/no-number-isnan"),
        "no-number-issafeinteger": require("./rules/no-number-issafeinteger"),
        "no-number-maxsafeinteger": require("./rules/no-number-maxsafeinteger"),
        "no-number-minsafeinteger": require("./rules/no-number-minsafeinteger"),
        "no-number-parsefloat": require("./rules/no-number-parsefloat"),
        "no-number-parseint": require("./rules/no-number-parseint"),
        "no-numeric-separators": require("./rules/no-numeric-separators"),
        "no-object-assign": require("./rules/no-object-assign"),
        "no-object-create": require("./rules/no-object-create"),
        "no-object-defineproperties": require("./rules/no-object-defineproperties"),
        "no-object-defineproperty": require("./rules/no-object-defineproperty"),
        "no-object-entries": require("./rules/no-object-entries"),
        "no-object-freeze": require("./rules/no-object-freeze"),
        "no-object-fromentries": require("./rules/no-object-fromentries"),
        "no-object-getownpropertydescriptor": require("./rules/no-object-getownpropertydescriptor"),
        "no-object-getownpropertydescriptors": require("./rules/no-object-getownpropertydescriptors"),
        "no-object-getownpropertynames": require("./rules/no-object-getownpropertynames"),
        "no-object-getownpropertysymbols": require("./rules/no-object-getownpropertysymbols"),
        "no-object-getprototypeof": require("./rules/no-object-getprototypeof"),
        "no-object-hasown": require("./rules/no-object-hasown"),
        "no-object-is": require("./rules/no-object-is"),
        "no-object-isextensible": require("./rules/no-object-isextensible"),
        "no-object-isfrozen": require("./rules/no-object-isfrozen"),
        "no-object-issealed": require("./rules/no-object-issealed"),
        "no-object-keys": require("./rules/no-object-keys"),
        "no-object-map-groupby": require("./rules/no-object-map-groupby"),
        "no-object-preventextensions": require("./rules/no-object-preventextensions"),
        "no-object-seal": require("./rules/no-object-seal"),
        "no-object-setprototypeof": require("./rules/no-object-setprototypeof"),
        "no-object-super-properties": require("./rules/no-object-super-properties"),
        "no-object-values": require("./rules/no-object-values"),
        "no-octal-numeric-literals": require("./rules/no-octal-numeric-literals"),
        "no-optional-catch-binding": require("./rules/no-optional-catch-binding"),
        "no-optional-chaining": require("./rules/no-optional-chaining"),
        "no-private-in": require("./rules/no-private-in"),
        "no-promise": require("./rules/no-promise"),
        "no-promise-all-settled": require("./rules/no-promise-all-settled"),
        "no-promise-any": require("./rules/no-promise-any"),
        "no-promise-prototype-finally": require("./rules/no-promise-prototype-finally"),
        "no-promise-withresolvers": require("./rules/no-promise-withresolvers"),
        "no-property-shorthands": require("./rules/no-property-shorthands"),
        "no-proxy": require("./rules/no-proxy"),
        "no-reflect": require("./rules/no-reflect"),
        "no-regexp-d-flag": require("./rules/no-regexp-d-flag"),
        "no-regexp-duplicate-named-capturing-groups": require("./rules/no-regexp-duplicate-named-capturing-groups"),
        "no-regexp-lookbehind-assertions": require("./rules/no-regexp-lookbehind-assertions"),
        "no-regexp-named-capture-groups": require("./rules/no-regexp-named-capture-groups"),
        "no-regexp-prototype-compile": require("./rules/no-regexp-prototype-compile"),
        "no-regexp-prototype-flags": require("./rules/no-regexp-prototype-flags"),
        "no-regexp-s-flag": require("./rules/no-regexp-s-flag"),
        "no-regexp-u-flag": require("./rules/no-regexp-u-flag"),
        "no-regexp-unicode-property-escapes": require("./rules/no-regexp-unicode-property-escapes"),
        "no-regexp-unicode-property-escapes-2019": require("./rules/no-regexp-unicode-property-escapes-2019"),
        "no-regexp-unicode-property-escapes-2020": require("./rules/no-regexp-unicode-property-escapes-2020"),
        "no-regexp-unicode-property-escapes-2021": require("./rules/no-regexp-unicode-property-escapes-2021"),
        "no-regexp-unicode-property-escapes-2022": require("./rules/no-regexp-unicode-property-escapes-2022"),
        "no-regexp-unicode-property-escapes-2023": require("./rules/no-regexp-unicode-property-escapes-2023"),
        "no-regexp-v-flag": require("./rules/no-regexp-v-flag"),
        "no-regexp-y-flag": require("./rules/no-regexp-y-flag"),
        "no-resizable-and-growable-arraybuffers": require("./rules/no-resizable-and-growable-arraybuffers"),
        "no-rest-parameters": require("./rules/no-rest-parameters"),
        "no-rest-spread-properties": require("./rules/no-rest-spread-properties"),
        "no-set": require("./rules/no-set"),
        "no-set-prototype-difference": require("./rules/no-set-prototype-difference"),
        "no-set-prototype-intersection": require("./rules/no-set-prototype-intersection"),
        "no-set-prototype-isdisjointfrom": require("./rules/no-set-prototype-isdisjointfrom"),
        "no-set-prototype-issubsetof": require("./rules/no-set-prototype-issubsetof"),
        "no-set-prototype-issupersetof": require("./rules/no-set-prototype-issupersetof"),
        "no-set-prototype-symmetricdifference": require("./rules/no-set-prototype-symmetricdifference"),
        "no-set-prototype-union": require("./rules/no-set-prototype-union"),
        "no-shadow-catch-param": require("./rules/no-shadow-catch-param"),
        "no-shared-array-buffer": require("./rules/no-shared-array-buffer"),
        "no-spread-elements": require("./rules/no-spread-elements"),
        "no-string-create-html-methods": require("./rules/no-string-create-html-methods"),
        "no-string-fromcodepoint": require("./rules/no-string-fromcodepoint"),
        "no-string-prototype-codepointat": require("./rules/no-string-prototype-codepointat"),
        "no-string-prototype-endswith": require("./rules/no-string-prototype-endswith"),
        "no-string-prototype-includes": require("./rules/no-string-prototype-includes"),
        "no-string-prototype-iswellformed-towellformed": require("./rules/no-string-prototype-iswellformed-towellformed"),
        "no-string-prototype-matchall": require("./rules/no-string-prototype-matchall"),
        "no-string-prototype-normalize": require("./rules/no-string-prototype-normalize"),
        "no-string-prototype-padstart-padend": require("./rules/no-string-prototype-padstart-padend"),
        "no-string-prototype-repeat": require("./rules/no-string-prototype-repeat"),
        "no-string-prototype-replaceall": require("./rules/no-string-prototype-replaceall"),
        "no-string-prototype-startswith": require("./rules/no-string-prototype-startswith"),
        "no-string-prototype-substr": require("./rules/no-string-prototype-substr"),
        "no-string-prototype-trim": require("./rules/no-string-prototype-trim"),
        "no-string-prototype-trimleft-trimright": require("./rules/no-string-prototype-trimleft-trimright"),
        "no-string-prototype-trimstart-trimend": require("./rules/no-string-prototype-trimstart-trimend"),
        "no-string-raw": require("./rules/no-string-raw"),
        "no-subclassing-builtins": require("./rules/no-subclassing-builtins"),
        "no-symbol": require("./rules/no-symbol"),
        "no-symbol-prototype-description": require("./rules/no-symbol-prototype-description"),
        "no-template-literals": require("./rules/no-template-literals"),
        "no-top-level-await": require("./rules/no-top-level-await"),
        "no-trailing-commas": require("./rules/no-trailing-commas"),
        "no-trailing-function-commas": require("./rules/no-trailing-function-commas"),
        "no-typed-arrays": require("./rules/no-typed-arrays"),
        "no-unicode-codepoint-escapes": require("./rules/no-unicode-codepoint-escapes"),
        "no-weak-map": require("./rules/no-weak-map"),
        "no-weak-set": require("./rules/no-weak-set"),
        "no-weakrefs": require("./rules/no-weakrefs"),
    },
}
