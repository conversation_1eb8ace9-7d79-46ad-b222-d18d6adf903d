/**
 * DON'T EDIT THIS FILE.
 * This file was generated by "scripts/update-lib-flat-configs.js" script.
 */
"use strict"

module.exports = {
    plugins: {
        get "es-x"() {
            return require("../../index.js")
        },
    },
    rules: {
        "es-x/no-regexp-duplicate-named-capturing-groups": "error",
        "es-x/no-set-prototype-difference": "error",
        "es-x/no-set-prototype-intersection": "error",
        "es-x/no-set-prototype-isdisjointfrom": "error",
        "es-x/no-set-prototype-issubsetof": "error",
        "es-x/no-set-prototype-issupersetof": "error",
        "es-x/no-set-prototype-symmetricdifference": "error",
        "es-x/no-set-prototype-union": "error",
        "es-x/no-arraybuffer-prototype-transfer": "error",
        "es-x/no-atomics-waitasync": "error",
        "es-x/no-object-map-groupby": "error",
        "es-x/no-promise-withresolvers": "error",
        "es-x/no-regexp-v-flag": "error",
        "es-x/no-resizable-and-growable-arraybuffers": "error",
        "es-x/no-string-prototype-iswellformed-towellformed": "error",
    },
}
