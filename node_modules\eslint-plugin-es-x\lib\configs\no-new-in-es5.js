/**
 * DON'T EDIT THIS FILE.
 * This file was generated by "scripts/update-lib-configs.js" script.
 */
"use strict"

module.exports = {
    plugins: ["es-x"],
    rules: {
        "es-x/no-accessor-properties": "error",
        "es-x/no-array-isarray": "error",
        "es-x/no-array-prototype-every": "error",
        "es-x/no-array-prototype-filter": "error",
        "es-x/no-array-prototype-foreach": "error",
        "es-x/no-array-prototype-indexof": "error",
        "es-x/no-array-prototype-lastindexof": "error",
        "es-x/no-array-prototype-map": "error",
        "es-x/no-array-prototype-reduce": "error",
        "es-x/no-array-prototype-reduceright": "error",
        "es-x/no-array-prototype-some": "error",
        "es-x/no-date-now": "error",
        "es-x/no-function-prototype-bind": "error",
        "es-x/no-json": "error",
        "es-x/no-keyword-properties": "error",
        "es-x/no-object-create": "error",
        "es-x/no-object-defineproperties": "error",
        "es-x/no-object-defineproperty": "error",
        "es-x/no-object-freeze": "error",
        "es-x/no-object-getownpropertydescriptor": "error",
        "es-x/no-object-getownpropertynames": "error",
        "es-x/no-object-getprototypeof": "error",
        "es-x/no-object-isextensible": "error",
        "es-x/no-object-isfrozen": "error",
        "es-x/no-object-issealed": "error",
        "es-x/no-object-keys": "error",
        "es-x/no-object-preventextensions": "error",
        "es-x/no-object-seal": "error",
        "es-x/no-string-prototype-trim": "error",
        "es-x/no-trailing-commas": "error",
    },
}
