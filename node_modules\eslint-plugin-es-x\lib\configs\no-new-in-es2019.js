/**
 * DON'T EDIT THIS FILE.
 * This file was generated by "scripts/update-lib-configs.js" script.
 */
"use strict"

module.exports = {
    plugins: ["es-x"],
    rules: {
        "es-x/no-array-prototype-flat": "error",
        "es-x/no-json-superset": "error",
        "es-x/no-object-fromentries": "error",
        "es-x/no-optional-catch-binding": "error",
        "es-x/no-regexp-unicode-property-escapes-2019": "error",
        "es-x/no-string-prototype-trimstart-trimend": "error",
        "es-x/no-symbol-prototype-description": "error",
    },
}
