{"name": "eslint-plugin-es-x", "version": "7.8.0", "description": "ESLint plugin about ECMAScript syntactic features.", "engines": {"node": "^14.18.0 || >=16.0.0"}, "main": "lib/index.js", "files": ["lib"], "peerDependencies": {"eslint": ">=8"}, "dependencies": {"@eslint-community/eslint-utils": "^4.1.2", "@eslint-community/regexpp": "^4.11.0", "eslint-compat-utils": "^0.5.1"}, "devDependencies": {"@typescript-eslint/parser": "^7.0.2", "env-cmd": "^10.1.0", "eslint": "^9.1.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-eslint-plugin": "^6.0.0", "eslint-plugin-n": "^17.8.1", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-vue": "^9.25.0", "espree": "^10.1.0", "globals": "^15.0.0", "jsdom": "^24.0.0", "mocha": "^10.0.0", "monaco-editor": "^0.50.0", "npm-run-all": "^4.1.5", "nyc": "^17.0.0", "opener": "^1.5.1", "rimraf": "^5.0.0", "semver": "^7.0.0", "typescript": "^5.0.2", "vite-plugin-eslint4b": "^0.4.0", "vitepress": "^1.0.0-rc.14", "vue-eslint-editor": "^1.1.0", "vue-eslint-parser": "^9.0.0"}, "scripts": {"clean": "rimraf .nyc_output coverage", "coverage": "nyc report -r lcov && opener ./coverage/lcov-report/index.html", "docs:build": "vitepress build docs", "docs:watch": "vitepress dev docs", "lint": "eslint lib tests scripts eslint-internal docs/.vitepress", "lint:fix": "eslint lib tests scripts eslint-internal docs/.vitepress --fix", "new": "node scripts/new-rule", "pretest": "npm run -s lint", "test": "npm run -s test:mocha", "test:mocha": "nyc mocha tests/**/*.{js,mjs} --reporter dot --timeout 60000", "test:debug": "mocha tests/**/*.{js,mjs} --reporter dot --timeout 60000", "update": "run-s update:*", "update:url": "npm run -s lint -- --fix", "update:configs": "node scripts/update-lib-configs", "update:flat-configs": "node scripts/update-lib-flat-configs", "update:index": "node scripts/update-lib-index", "update:doc": "node scripts/update-docs-readme", "update:rule-docs": "node scripts/update-docs-rules", "update:config-docs": "node scripts/update-docs-configs", "resource-update:unicode-properties": "node scripts/update-unicode-properties", "preversion": "npm test", "version": "env-cmd -e version run-s update:* && git add .", "postversion": "git push && git push --tags", "watch": "mocha tests/**/*.{js,mjs} --reporter progress --watch --growl"}, "repository": {"type": "git", "url": "git+https://github.com/eslint-community/eslint-plugin-es-x.git"}, "keywords": ["eslint", "plugin", "eslintplugin"], "author": "<PERSON><PERSON> (https://github.com/mysticatea)", "license": "MIT", "bugs": {"url": "https://github.com/eslint-community/eslint-plugin-es-x/issues"}, "homepage": "https://github.com/eslint-community/eslint-plugin-es-x#readme", "funding": ["https://github.com/sponsors/ota-meshi", "https://opencollective.com/eslint"]}